'use client'
import { AppSidebar, NavItem } from '@/components/custom/app-sidebar'
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'
import { LayoutDashboard, ScanEye } from 'lucide-react'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import Header from './components/header'
// Menu items.
const items: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Guidance Templates',
    url: '/guidance-templates',
    icon: Scan<PERSON>ye,
  },
  {
    title: 'Premises',
    url: '/premises',
    icon: Scan<PERSON><PERSON>,
  },
  {
    title: 'Incidents',
    url: '/incidents',
    icon: Scan<PERSON>ye,
  },
  {
    title: 'Cameras',
    url: '/cameras',
    icon: ScanEye,
  },
  {
    title: 'Guards',
    url: '/guards',
    icon: ScanEye,
  },
  {
    title: 'Settings',
    url: '/settings',
    icon: ScanEye,
  },
]
function OperatorLayout({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary>
      <SidebarProvider>
        <div className="w-fit relative">
          <AppSidebar items={items} />
          <SidebarTrigger className="top-0 right-[-25px] absolute " />
        </div>
        <div className="flex flex-col w-full">
          <Header />
          <div className="p-2"> {children}</div>
        </div>
      </SidebarProvider>
    </ErrorBoundary>
  )
}

export default OperatorLayout
