import { QueryParams } from "@/types";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}
export function createSearchParams(queryParams: QueryParams) {
	return new URLSearchParams(
		Object.entries(queryParams).reduce(
			(acc, [key, value]) => {
				if (value !== undefined) acc[key] = String(value);
				return acc;
			},
			{} as Record<string, string>,
		),
	);
}
